"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processPdfsInFolder = processPdfsInFolder;
const generative_ai_1 = require("@google/generative-ai");
const dotenv = __importStar(require("dotenv"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs/promises")); // Use promises-based fs
const sharp_1 = __importDefault(require("sharp")); // For image processing (resizing, converting, etc.)
// import { PDFDocument } from 'pdf-lib'; // If you need to manipulate PDFs directly, though your example focuses on images
// --- Configuration ---
dotenv.config(); // Load environment variables from .env file
const API_KEY = process.env.GOOGLE_API_KEY;
if (!API_KEY) {
    console.error("Error: GOOGLE_API_KEY not found in .env file.");
    process.exit(1);
}
const genAI = new generative_ai_1.GoogleGenerativeAI(API_KEY);
const model = genAI.getGenerativeModel({
    model: "gemini-2.0-flash",
});
// --- Utility Functions ---
/**
 * Converts a PDF page to a high-resolution image using an external tool (e.g., Poppler via a child process).
 * THIS IS A PLACEHOLDER.
 * For a real-world solution, you would need to:
 * 1. Install Poppler (system dependency).
 * 2. Use a Node.js library that wraps Poppler (e.g., `pdf-poppler-js` or `node-poppler`).
 *    Or, spawn a child process to run `pdftocairo` or a Python script that uses `pdf2image`.
 *
 * For demonstration, this function will assume a direct image file path if a PDF path is given.
 * In a production scenario, you would implement the actual PDF-to-image conversion here.
 * As a fallback, if you truly cannot install external tools, you might have to convert PDFs to images manually
 * or use a cloud service before running this script.
 *
 * @param pdfPath The path to the PDF file.
 * @param outputFolder The folder to save images to.
 * @param dpi Dots per inch for image resolution.
 * @returns An array of paths to the generated image files.
 */
async function convertPdfToImages(pdfPath, outputFolder, dpi = 300) {
    console.warn(`[WARNING]: 'convertPdfToImages' is a placeholder. For real PDF conversion, you need a system-level PDF renderer like Poppler.`);
    console.warn(`If you are seeing this message, ensure your 'input_files' are already images or implement actual PDF-to-image conversion.`);
    // --- Placeholder Implementation for Demonstration ---
    // In a real scenario, this would involve a library like 'pdf-poppler-js' or 'child_process' to call an external tool.
    // For this example, let's assume we are just processing directly given image files.
    // If you pass a PDF, this will simply return an empty array, or you could throw an error.
    const fileExtension = path.extname(pdfPath).toLowerCase();
    if (fileExtension === '.pdf') {
        // You would integrate a PDF conversion library here.
        // Example using a hypothetical 'pdf-poppler-js' (requires Poppler installed):
        /*
        const { Poppler } = require('pdf-poppler-js');
        const poppler = new Poppler();
        await fs.mkdir(outputFolder, { recursive: true });
        const images = await poppler.pdfToPNG(pdfPath, outputFolder, { dpi });
        return images.map(imgName => path.join(outputFolder, imgName));
        */
        console.error(`Error: PDF conversion for '${pdfPath}' not implemented. Please provide image files or implement PDF-to-image conversion.`);
        return [];
    }
    else if (['.jpg', '.jpeg', '.png', '.webp'].includes(fileExtension)) {
        // If it's already an image, just return its path as if it was converted.
        await fs.mkdir(outputFolder, { recursive: true });
        const baseName = path.basename(pdfPath);
        const outputPath = path.join(outputFolder, baseName);
        await fs.copyFile(pdfPath, outputPath);
        return [outputPath];
    }
    else {
        console.error(`Unsupported file type: ${fileExtension}. Please provide PDF or image files.`);
        return [];
    }
}
/**
 * Groups image paths into batches.
 * @param imagePaths Array of image file paths.
 * @param batchSize The maximum number of images per batch.
 * @returns An array of arrays, where each inner array is a batch of image paths.
 */
function batchImages(imagePaths, batchSize = 50) {
    const batches = [];
    for (let i = 0; i < imagePaths.length; i += batchSize) {
        batches.push(imagePaths.slice(i, i + batchSize));
    }
    return batches;
}
/**
 * Converts an image file to a Base64 string for Gemini API.
 * @param imagePath Path to the image file.
 * @returns A Promise that resolves to an object with mimeType and base64 data.
 */
async function fileToGenerativePart(imagePath) {
    const data = await fs.readFile(imagePath);
    const mimeType = (await (0, sharp_1.default)(data).metadata()).format === 'jpeg' ? 'image/jpeg' : 'image/png'; // infer mime based on content
    return {
        inlineData: {
            data: Buffer.from(data).toString('base64'),
            mimeType: mimeType || 'image/jpeg', // Fallback to jpeg if detection fails
        },
    };
}
/**
 * Processes images with Gemini 2.0 Flash for OCR.
 * @param imagePaths Array of paths to image files.
 * @param instruction The OCR instruction for Gemini.
 * @returns The extracted text content.
 */
async function ocrWithGemini(imagePaths, instruction) {
    if (imagePaths.length === 0) {
        return "";
    }
    const imageParts = await Promise.all(imagePaths.map(fileToGenerativePart));
    const prompt = `
    ${instruction}
    
    These are pages from a document. Extract all text content while preserving the structure.
    Pay special attention to tables, columns, headers, and any structured content.
    Maintain paragraph breaks and formatting.
    `;
    try {
        const result = await model.generateContent([prompt, ...imageParts]);
        const response = await result.response;
        return response.text();
    }
    catch (error) {
        console.error(`Error during Gemini OCR processing for images: ${imagePaths.join(', ')}. Error:`, error);
        return `[ERROR: OCR failed for these images. ${error instanceof Error ? error.message : String(error)}]`;
    }
}
// --- Specialized OCR Functions ---
async function ocrComplexDocument(imagePaths) {
    const instruction = `
    Extract ALL text content from these document pages.
    For tables:
    1. Maintain the table structure using markdown table format
    2. Preserve all column headers and row labels
    3. Ensure numerical data is accurately captured
    
    For multi-column layouts:
    1. Process columns from left to right
    2. Clearly separate content from different columns
    
    For charts and graphs:
    1. Describe the chart type
    2. Extract any visible axis labels, legends, and data points
    3. Extract any title or caption
    
    Preserve all headers, footers, page numbers, and footnotes.
    `;
    return ocrWithGemini(imagePaths, instruction);
}
async function ocrFinancialDocument(imagePaths) {
    const instruction = `
    Extract ALL text content from these financial document pages.
    
    Pay particular attention to:
    1. All numerical values and ensure they're accurately transcribed
    2. Currency symbols and their correct association with numbers
    3. Financial tables - maintain their exact structure and alignment
    4. Balance sheets, income statements, and cash flow statements
    5. Footnotes and disclosures - these often contain crucial information
    6. Any dates associated with financial periods
    
    Format tables using markdown table syntax to preserve their structure.
    `;
    return ocrWithGemini(imagePaths, instruction);
}
/**
 * Verifies the quality of OCR results for a specific page.
 * NOTE: This function requires the *original image file* and the *extracted text*.
 * It will send both to Gemini for comparison.
 * @param imagePath Path to the original image file.
 * @param extractedText The text extracted from that image.
 * @returns Gemini's feedback on the OCR quality.
 */
async function verifyOcrQuality(imagePath, extractedText) {
    const imagePart = await fileToGenerativePart(imagePath);
    const prompt = `
    I have a document page and text that was extracted from it using OCR.
    
    Compare the original image with the extracted text and identify any errors or omissions.
    Focus on:
    1. Missing text
    2. Incorrectly recognized characters
    3. Table structure issues
    4. Issues with special characters or symbols
    
    Extracted text:
    ${extractedText}
    `;
    try {
        const result = await model.generateContent([prompt, imagePart]);
        const response = await result.response;
        return response.text();
    }
    catch (error) {
        console.error(`Error during quality verification for image: ${imagePath}. Error:`, error);
        return `[ERROR: Quality verification failed. ${error instanceof Error ? error.message : String(error)}]`;
    }
}
// --- Large Document Processing ---
/**
 * Processes a large PDF by converting it to images, batching them, and OCRing each batch.
 * @param filePath The path to the PDF file (or image file for demo).
 * @param tempImagesFolder A temporary folder to store generated images.
 * @param outputFile The path to save the final extracted text.
 * @returns The full extracted text.
 */
async function processLargeDocument(filePath, tempImagesFolder, outputFile) {
    console.log(`Processing document: ${filePath}`);
    // Ensure tempImagesFolder exists and is clean
    await fs.rm(tempImagesFolder, { recursive: true, force: true });
    await fs.mkdir(tempImagesFolder, { recursive: true });
    // Convert PDF to images (or copy images if already images)
    const imagePaths = await convertPdfToImages(filePath, tempImagesFolder);
    if (imagePaths.length === 0) {
        console.error(`No images generated or found for ${filePath}. Skipping.`);
        return "";
    }
    console.log(`Converted ${imagePaths.length} pages to images.`);
    // Create batches of images
    // Adjust batch size based on document complexity and Gemini's token limits (image data also counts towards context)
    const batches = batchImages(imagePaths, 15); // Smaller batch size for safety with image data
    let fullText = "";
    for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`Processing batch ${i + 1}/${batches.length} (${batch.length} images)...`);
        const batchText = await ocrWithGemini(batch, "Extract all text, maintaining document structure");
        fullText += `\n\n--- BATCH ${i + 1} ---\n\n${batchText}`;
    }
    // Save the full extracted text
    await fs.writeFile(outputFile, fullText, 'utf-8');
    console.log(`Full extracted text saved to ${outputFile}`);
    // Clean up temporary images
    await fs.rm(tempImagesFolder, { recursive: true, force: true });
    console.log(`Cleaned up temporary images in ${tempImagesFolder}`);
    return fullText;
}
/**
 * Harmonizes text extracted from batches for better coherence.
 * @param extractedText The full text extracted from batches, possibly with markers.
 * @returns The harmonized text.
 */
async function harmonizeDocument(extractedText) {
    const prompt = `
    The following text was extracted from a large document in batches.
    Harmonize the content by:
    1. Removing any batch separation markers (e.g., "--- BATCH X ---")
    2. Ensuring consistent formatting throughout
    3. Fixing any table structure issues at batch boundaries
    4. Ensuring paragraph and section flow is natural across batch boundaries
    
    Original extracted text:
    ${extractedText}
    `;
    try {
        const result = await model.generateContent(prompt);
        const response = await result.response;
        return response.text();
    }
    catch (error) {
        console.error(`Error during document harmonization. Error:`, error);
        return `[ERROR: Harmonization failed. ${error instanceof Error ? error.message : String(error)}]\n\n${extractedText}`;
    }
}
// --- Specialized Use Case: Historical Document OCR ---
async function historicalDocumentOcr(imagePaths) {
    const instruction = `
    Extract text from these historical document images.
    
    Consider the following challenges:
    1. Aged paper with stains or discoloration
    2. Faded ink or typefaces
    3. Old-fashioned typography and ligatures
    4. Handwritten annotations
    5. Non-standard page layouts
    
    Prioritize accuracy over format preservation when necessary.
    Note any text that appears uncertain with [?].
    `;
    const extractedText = await ocrWithGemini(imagePaths, instruction);
    // Additional context-based correction
    const correctionPrompt = `
    The following text was extracted from a historical document that may have quality issues.
    Review the text for typical OCR errors in historical documents:
    1. Fix words that were likely misinterpreted due to aged paper or faded ink
    2. Correct archaic spellings only if they appear to be OCR errors (not if they're actually period-appropriate)
    3. Resolve any uncertain text marked with [?] if context makes the correct reading clear
    
    Original text:
    ${extractedText}
    `;
    try {
        const result = await model.generateContent(correctionPrompt);
        const response = await result.response;
        return response.text();
    }
    catch (error) {
        console.error(`Error during historical document correction. Error:`, error);
        return `[ERROR: Historical document correction failed. ${error instanceof Error ? error.message : String(error)}]\n\n${extractedText}`;
    }
}
/**
 * Main function to process all PDF files in an input folder.
 * @param inputFolder Path to the folder containing PDF files.
 * @param outputFolder Path to the folder where OCR results will be saved.
 */
async function processPdfsInFolder(inputFolder, outputFolder) {
    try {
        await fs.mkdir(outputFolder, { recursive: true }); // Ensure output folder exists
        const files = await fs.readdir(inputFolder);
        const pdfFiles = files.filter(file => {
            const ext = path.extname(file).toLowerCase();
            return ext === '.pdf' || ext === '.jpg' || ext === '.jpeg' || ext === '.png'; // Also allow image files for demo
        });
        if (pdfFiles.length === 0) {
            console.log(`No PDF or image files found in ${inputFolder}.`);
            return;
        }
        for (const file of pdfFiles) {
            const inputFilePath = path.join(inputFolder, file);
            const outputFileName = `${path.parse(file).name}.txt`;
            const outputFilePath = path.join(outputFolder, outputFileName);
            const tempImagesDir = path.join(outputFolder, `temp_images_${path.parse(file).name}`);
            console.log(`--- Starting OCR for ${file} ---`);
            let extractedText = await processLargeDocument(inputFilePath, tempImagesDir, outputFilePath);
            if (extractedText) {
                console.log(`Harmonizing extracted text for ${file}...`);
                const harmonizedText = await harmonizeDocument(extractedText);
                await fs.writeFile(outputFilePath, harmonizedText, 'utf-8');
                console.log(`Harmonized text saved to ${outputFilePath}`);
                // Optional: Perform quality check on a random page (for demonstration)
                // const imagePathsForQualityCheck = await convertPdfToImages(inputFilePath, tempImagesDir + '_qc'); // Re-convert for QC if needed
                // if (imagePathsForQualityCheck.length > 0) {
                //     const randomPageIndex = Math.floor(Math.random() * imagePathsForQualityCheck.length);
                //     const randomImagePath = imagePathsForQualityCheck[randomPageIndex];
                //     console.log(`Performing quality check on a random page (${path.basename(randomImagePath)})...`);
                //     // You'd need to extract just that page's text for precise QC, or re-OCR that page.
                //     // For simplicity, we'll just use the first few lines of the harmonized text.
                //     const qualityFeedback = await verifyOcrQuality(randomImagePath, harmonizedText.substring(0, 500));
                //     console.log(`Quality Feedback for ${file}:\n${qualityFeedback}`);
                //     await fs.rm(tempImagesDir + '_qc', { recursive: true, force: true }); // Clean up QC temp
                // }
            }
            console.log(`--- Finished OCR for ${file} ---\n`);
        }
    }
    catch (error) {
        console.error("An error occurred during folder processing:", error);
    }
}
// --- How to run this function ---
// You would typically call this from an `index.ts` or `app.ts` file.
// Example of how to run this directly for testing:
// (Uncomment the following lines to run when compiled and executed)
// const INPUT_DIR = path.join(__dirname, '..', 'input_files');
// const OUTPUT_DIR = path.join(__dirname, '..', 'output_files');
// (async () => {
//     console.log(`Starting OCR process for files in: ${INPUT_DIR}`);
//     await processPdfsInFolder(INPUT_DIR, OUTPUT_DIR);
//     console.log(`OCR process completed. Results saved to: ${OUTPUT_DIR}`);
// })();
// To make it directly runnable as a script, you can add this to your package.json:
// "scripts": {
//   "start": "ts-node src/ocrProcessor.ts" // If you install ts-node
//   "build": "tsc",
//   "run-ocr": "node dist/ocrProcessor.js" // After building
// }
