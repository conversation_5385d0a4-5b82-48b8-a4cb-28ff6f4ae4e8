{"name": "@types/sharp", "version": "0.31.1", "description": "TypeScript definitions for sharp", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/sharp", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/wooseopkim", "githubUsername": "woos<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/BTOdell", "githubUsername": "BTOdell"}, {"name": "<PERSON>", "url": "https://github.com/Jamie<PERSON>bury", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Fdebijl", "githubUsername": "Fdebijl"}, {"name": "<PERSON>", "url": "https://github.com/billykwok", "githubUsername": "billykwok"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/rexxars", "githubUsername": "rexxars"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/sharp"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "57593dfe01e99da9b35e4f79b6664562dfb226c82eb9adaf7e8d2214fead75e5", "typeScriptVersion": "4.2"}