name: Run tests

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [14.x, 16.x, 18.x, 20.x]

    name: Node.js ${{ matrix.node-version }}

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install GraphicsMagick and Ghostscript
        run: |
          sudo apt-get update
          sudo apt-get install -y graphicsmagick ghostscript
      - run: npm install

      # Run tests only (for pull requests or non-master branches)
      - name: Run tests only
        if: github.ref != 'refs/heads/master'
        run: npm test

      # Run tests with CodeClimate reporting (only on master branch)
      - name: Run tests with CodeClimate
        if: github.ref == 'refs/heads/master'
        uses: paambaati/codeclimate-action@v5
        env:
          CC_TEST_REPORTER_ID: ${{ secrets.CC_TEST_REPORTER_ID }}
        with:
          coverageCommand: npm test

