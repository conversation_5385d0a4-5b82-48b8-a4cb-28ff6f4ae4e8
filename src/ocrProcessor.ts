import {
  GoogleGenerativeAI,
  GenerativeModel,
  Part,
} from "@google/generative-ai";
import * as dotenv from "dotenv";
import * as path from "path";
import * as fs from "fs/promises"; // Use promises-based fs
import sharp from "sharp"; // For image processing (resizing, converting, etc.)
import { fromPath } from "pdf2pic";
// import { PDFDocument } from 'pdf-lib'; // If you need to manipulate PDFs directly, though your example focuses on images

// --- Configuration ---
dotenv.config(); // Load environment variables from .env file
const API_KEY: string | undefined = process.env.GOOGLE_API_KEY;

if (!API_KEY) {
  console.error("Error: GOOGLE_API_KEY not found in .env file.");
  process.exit(1);
}

const genAI: GoogleGenerativeAI = new GoogleGenerativeAI(API_KEY);
const model: GenerativeModel = genAI.getGenerativeModel({
  model: "gemini-2.0-flash",
});

// --- Utility Functions ---

/**
 * Converts a PDF to high-resolution images using pdf2pic.
 * Requires GraphicsMagick to be installed on the system (brew install graphicsmagick on macOS).
 *
 * @param pdfPath The path to the PDF file.
 * @param outputFolder The folder to save images to.
 * @param dpi Dots per inch for image resolution.
 * @returns An array of paths to the generated image files.
 */
async function convertPdfToImages(
  pdfPath: string,
  outputFolder: string,
  dpi: number = 300
): Promise<string[]> {
  const fileExtension = path.extname(pdfPath).toLowerCase();

  if (fileExtension === ".pdf") {
    try {
      console.log(`Converting PDF to images: ${pdfPath}`);
      await fs.mkdir(outputFolder, { recursive: true });

      // Configure pdf2pic options
      const convert = fromPath(pdfPath, {
        density: dpi,
        saveFilename: path.parse(pdfPath).name,
        savePath: outputFolder,
        format: "png",
        width: 2000,
        height: 2000,
      });

      // Convert all pages
      const results = await convert.bulk(-1);

      // Get the file paths from the results
      const imageFiles = results.map((result: any) => result.path).sort();

      console.log(`Successfully converted PDF to ${imageFiles.length} images`);
      return imageFiles;
    } catch (error) {
      console.error(`Error converting PDF to images: ${error}`);
      console.error(
        `Make sure GraphicsMagick is installed on your system. On macOS: brew install graphicsmagick`
      );
      return [];
    }
  } else if ([".jpg", ".jpeg", ".png", ".webp"].includes(fileExtension)) {
    // If it's already an image, just return its path as if it was converted.
    await fs.mkdir(outputFolder, { recursive: true });
    const baseName = path.basename(pdfPath);
    const outputPath = path.join(outputFolder, baseName);
    await fs.copyFile(pdfPath, outputPath);
    return [outputPath];
  } else {
    console.error(
      `Unsupported file type: ${fileExtension}. Please provide PDF or image files.`
    );
    return [];
  }
}

/**
 * Groups image paths into batches.
 * @param imagePaths Array of image file paths.
 * @param batchSize The maximum number of images per batch.
 * @returns An array of arrays, where each inner array is a batch of image paths.
 */
function batchImages<T>(imagePaths: T[], batchSize: number = 50): T[][] {
  const batches: T[][] = [];
  for (let i = 0; i < imagePaths.length; i += batchSize) {
    batches.push(imagePaths.slice(i, i + batchSize));
  }
  return batches;
}

/**
 * Converts an image file to a Base64 string for Gemini API.
 * @param imagePath Path to the image file.
 * @returns A Promise that resolves to an object with mimeType and base64 data.
 */
async function fileToGenerativePart(imagePath: string): Promise<Part> {
  const data = await fs.readFile(imagePath);
  const mimeType =
    (await sharp(data).metadata()).format === "jpeg"
      ? "image/jpeg"
      : "image/png"; // infer mime based on content
  return {
    inlineData: {
      data: Buffer.from(data).toString("base64"),
      mimeType: mimeType || "image/jpeg", // Fallback to jpeg if detection fails
    },
  };
}

/**
 * Processes images with Gemini 2.0 Flash for OCR.
 * @param imagePaths Array of paths to image files.
 * @param instruction The OCR instruction for Gemini.
 * @returns The extracted text content.
 */
async function ocrWithGemini(
  imagePaths: string[],
  instruction: string
): Promise<string> {
  if (imagePaths.length === 0) {
    return "";
  }
  const imageParts: Part[] = await Promise.all(
    imagePaths.map(fileToGenerativePart)
  );

  const prompt: string = `
    ${instruction}
    
    These are pages from a document. Extract all text content while preserving the structure.
    Pay special attention to tables, columns, headers, and any structured content.
    Maintain paragraph breaks and formatting.

    don't leave any text out.

    extract all the text in both arabic and english without changing the original text or translating any words.

    For tables:
    1. Maintain the table structure using markdown table format
    2. Preserve all column headers and row labels
    3. Ensure numerical data is accurately captured
    
    For multi-column layouts:
    1. Process columns from left to right
    2. Clearly separate content from different columns
    
    For charts and graphs:
    1. Describe the chart type
    2. Extract any visible axis labels, legends, and data points
    3. Extract any title or caption


    don't output any explanation or introduction. just output the text.
    if there is no text in the image, output an empty string.
    `;

  try {
    const result = await model.generateContent([prompt, ...imageParts]);
    const response = await result.response;
    return response.text();
  } catch (error) {
    console.error(
      `Error during Gemini OCR processing for images: ${imagePaths.join(", ")}. Error:`,
      error
    );
    return `[ERROR: OCR failed for these images. ${error instanceof Error ? error.message : String(error)}]`;
  }
}

/**
 * Verifies the quality of OCR results for a specific page.
 * NOTE: This function requires the *original image file* and the *extracted text*.
 * It will send both to Gemini for comparison.
 * @param imagePath Path to the original image file.
 * @param extractedText The text extracted from that image.
 * @returns Gemini's feedback on the OCR quality.
 */
async function verifyOcrQuality(
  imagePath: string,
  extractedText: string
): Promise<string> {
  const imagePart = await fileToGenerativePart(imagePath);

  const prompt = `
    I have a document page and text that was extracted from it using OCR.
    
    Compare the original image with the extracted text and identify any errors or omissions.
    Focus on:
    1. Missing text
    2. Incorrectly recognized characters
    3. Table structure issues
    4. Issues with special characters or symbols
    
    Extracted text:
    ${extractedText}
    `;

  try {
    const result = await model.generateContent([prompt, imagePart]);
    const response = await result.response;
    return response.text();
  } catch (error) {
    console.error(
      `Error during quality verification for image: ${imagePath}. Error:`,
      error
    );
    return `[ERROR: Quality verification failed. ${error instanceof Error ? error.message : String(error)}]`;
  }
}

// --- Large Document Processing ---

/**
 * Processes a large PDF by converting it to images, batching them, and OCRing each batch.
 * @param filePath The path to the PDF file (or image file for demo).
 * @param tempImagesFolder A temporary folder to store generated images.
 * @param outputFile The path to save the final extracted text.
 * @returns The full extracted text.
 */
async function processLargeDocument(
  filePath: string,
  tempImagesFolder: string,
  outputFile: string
): Promise<string> {
  console.log(`Processing document: ${filePath}`);
  // Ensure tempImagesFolder exists and is clean
  await fs.rm(tempImagesFolder, { recursive: true, force: true });
  await fs.mkdir(tempImagesFolder, { recursive: true });

  // Convert PDF to images (or copy images if already images)
  const imagePaths = await convertPdfToImages(filePath, tempImagesFolder);
  if (imagePaths.length === 0) {
    console.error(`No images generated or found for ${filePath}. Skipping.`);
    return "";
  }
  console.log(`Converted ${imagePaths.length} pages to images.`);

  // Create batches of images
  // Adjust batch size based on document complexity and Gemini's token limits (image data also counts towards context)
  const batches = batchImages(imagePaths, 1); // Smaller batch size for safety with image data

  let fullText = "";
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    console.log(
      `Processing batch ${i + 1}/${batches.length} (${batch.length} images)...`
    );
    const batchText = await ocrWithGemini(
      batch,
      "Extract all text, maintaining document structure"
    );
    console.log(batchText);

    fullText += `\n\n----${i}-----\n\n${batchText}`;
  }

  // Save the full extracted text
  await fs.writeFile(outputFile, fullText, "utf-8");
  console.log(`Full extracted text saved to ${outputFile}`);

  // Clean up temporary images
  await fs.rm(tempImagesFolder, { recursive: true, force: true });
  console.log(`Cleaned up temporary images in ${tempImagesFolder}`);

  return fullText;
}

/**
 * Harmonizes text extracted from batches for better coherence.
 * @param extractedText The full text extracted from batches, possibly with markers.
 * @returns The harmonized text.
 */
async function harmonizeDocument(extractedText: string): Promise<string> {
  const prompt = `
    The following text was extracted from a large document in batches.
    Harmonize the content by:
    1. Removing any batch separation markers (e.g., "--- BATCH X ---")
    2. Ensuring consistent formatting throughout
    3. Fixing any table structure issues at batch boundaries
    4. Ensuring paragraph and section flow is natural across batch boundaries
    
    Original extracted text:
    ${extractedText}
    `;

  try {
    const result = await model.generateContent(prompt);
    const response = await result.response;
    return response.text();
  } catch (error) {
    console.error(`Error during document harmonization. Error:`, error);
    return `[ERROR: Harmonization failed. ${error instanceof Error ? error.message : String(error)}]\n\n${extractedText}`;
  }
}

// --- Specialized Use Case: Historical Document OCR ---
async function historicalDocumentOcr(imagePaths: string[]): Promise<string> {
  const instruction = `
    Extract text from these historical document images.
    
    Consider the following challenges:
    1. Aged paper with stains or discoloration
    2. Faded ink or typefaces
    3. Old-fashioned typography and ligatures
    4. Handwritten annotations
    5. Non-standard page layouts
    
    Prioritize accuracy over format preservation when necessary.
    Note any text that appears uncertain with [?].
    `;

  const extractedText = await ocrWithGemini(imagePaths, instruction);

  // Additional context-based correction
  const correctionPrompt = `
    The following text was extracted from a historical document that may have quality issues.
    Review the text for typical OCR errors in historical documents:
    1. Fix words that were likely misinterpreted due to aged paper or faded ink
    2. Correct archaic spellings only if they appear to be OCR errors (not if they're actually period-appropriate)
    3. Resolve any uncertain text marked with [?] if context makes the correct reading clear
    
    Original text:
    ${extractedText}
    `;

  try {
    const result = await model.generateContent(correctionPrompt);
    const response = await result.response;
    return response.text();
  } catch (error) {
    console.error(`Error during historical document correction. Error:`, error);
    return `[ERROR: Historical document correction failed. ${error instanceof Error ? error.message : String(error)}]\n\n${extractedText}`;
  }
}

/**
 * Main function to process all PDF files in an input folder.
 * @param inputFolder Path to the folder containing PDF files.
 * @param outputFolder Path to the folder where OCR results will be saved.
 */
export async function processPdfsInFolder(
  inputFolder: string,
  outputFolder: string
): Promise<void> {
  try {
    await fs.mkdir(outputFolder, { recursive: true }); // Ensure output folder exists

    const files = await fs.readdir(inputFolder);
    const pdfFiles = files.filter((file) => {
      const ext = path.extname(file).toLowerCase();
      return (
        ext === ".pdf" || ext === ".jpg" || ext === ".jpeg" || ext === ".png"
      ); // Also allow image files for demo
    });

    if (pdfFiles.length === 0) {
      console.log(`No PDF or image files found in ${inputFolder}.`);
      return;
    }

    for (const file of pdfFiles) {
      const inputFilePath = path.join(inputFolder, file);
      const outputFileName = `${path.parse(file).name}.txt`;
      const outputFilePath = path.join(outputFolder, outputFileName);
      const tempImagesDir = path.join(
        outputFolder,
        `temp_images_${path.parse(file).name}`
      );

      console.log(`--- Starting OCR for ${file} ---`);
      let extractedText = await processLargeDocument(
        inputFilePath,
        tempImagesDir,
        outputFilePath
      );

      //   if (extractedText) {
      //     console.log(`Harmonizing extracted text for ${file}...`);
      //     const harmonizedText = await harmonizeDocument(extractedText);
      //     await fs.writeFile(outputFilePath, harmonizedText, "utf-8");
      //     console.log(`Harmonized text saved to ${outputFilePath}`);

      //     // Optional: Perform quality check on a random page (for demonstration)
      //     // const imagePathsForQualityCheck = await convertPdfToImages(inputFilePath, tempImagesDir + '_qc'); // Re-convert for QC if needed
      //     // if (imagePathsForQualityCheck.length > 0) {
      //     //     const randomPageIndex = Math.floor(Math.random() * imagePathsForQualityCheck.length);
      //     //     const randomImagePath = imagePathsForQualityCheck[randomPageIndex];
      //     //     console.log(`Performing quality check on a random page (${path.basename(randomImagePath)})...`);
      //     //     // You'd need to extract just that page's text for precise QC, or re-OCR that page.
      //     //     // For simplicity, we'll just use the first few lines of the harmonized text.
      //     //     const qualityFeedback = await verifyOcrQuality(randomImagePath, harmonizedText.substring(0, 500));
      //     //     console.log(`Quality Feedback for ${file}:\n${qualityFeedback}`);
      //     //     await fs.rm(tempImagesDir + '_qc', { recursive: true, force: true }); // Clean up QC temp
      //     // }
      //   }
      console.log(`--- Finished OCR for ${file} ---\n`);
    }
  } catch (error) {
    console.error("An error occurred during folder processing:", error);
  }
}

// --- How to run this function ---
// You would typically call this from an `index.ts` or `app.ts` file.

// Example of how to run this directly for testing:
// (Uncomment the following lines to run when compiled and executed)

// const INPUT_DIR = path.join(__dirname, '..', 'input_files');
// const OUTPUT_DIR = path.join(__dirname, '..', 'output_files');

// (async () => {
//     console.log(`Starting OCR process for files in: ${INPUT_DIR}`);
//     await processPdfsInFolder(INPUT_DIR, OUTPUT_DIR);
//     console.log(`OCR process completed. Results saved to: ${OUTPUT_DIR}`);
// })();

// To make it directly runnable as a script, you can add this to your package.json:
// "scripts": {
//   "start": "ts-node src/ocrProcessor.ts" // If you install ts-node
//   "build": "tsc",
//   "run-ocr": "node dist/ocrProcessor.js" // After building
// }
