{"name": "gemini-ocr-processor", "version": "1.0.0", "description": "", "main": "dist/run.js", "scripts": {"build": "tsc", "start": "node dist/run.js", "dev": "ts-node src/run.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@google/generative-ai": "^0.24.1", "dotenv": "^17.0.0", "pdf-lib": "^1.17.1", "pdf2pic": "^3.2.0", "sharp": "^0.34.2"}, "devDependencies": {"@types/node": "^24.0.7", "@types/sharp": "^0.31.1", "typescript": "^5.8.3"}}